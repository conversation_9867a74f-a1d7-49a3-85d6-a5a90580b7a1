# Package Template

## Description

Template repository to create packages.

## Usage

1. Create a new github project and use this project as template. Follow this [link](https://docs.github.com/en/enterprise-cloud@latest/repositories/creating-and-managing-repositories/creating-a-repository-from-a-template) for detailed instructions on how to create a repository from a template.
2. Update **package.json** file.
  - Update the **name** field with your package name.
  - Update the **description** field with your package description.
  - Update the **repository** with your package repository URL.
3. Update **README.sample.md** with your package information and replace this **README.md** with it.
4. Rename **package-template-github** directory to **.github** (remove the existing one used for this repository).