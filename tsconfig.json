{
  "extends": "./node_modules/@tsconfig/strictest/tsconfig.json",
  "compilerOptions": {
    "allowJs": true,
    "baseUrl": ".",
    "declaration": true,
    "esModuleInterop": true,
    "importHelpers": true,
    "inlineSourceMap": true,
    "inlineSources": true,
    "isolatedModules": true,
    "jsx": "react-jsx",
    "lib": ["dom", "dom.iterable", "esnext"],
    "moduleResolution": "node",
    "noEmitOnError": true,
    "outDir": "./dist",
    "paths": {
      "@/*": ["./src/*"]
    },
    "resolveJsonModule": true,
    "rootDir": "./src",
    "skipLibCheck": true,
    "target": "ES5",
    "typeRoots": ["./@types", "./node_modules/@types"]
  },
  "include": ["src"],
  "exclude": ["dist", "node_modules"],
}
