{"name": "@unlockre/package-template", "type": "module", "version": "1.0.0", "description": "Package Template", "repository": "https://github.com/unlockre/package-template", "scripts": {"build": "yarn build:clean && yarn build:code", "build:clean": "rm -rf dist", "build:code": "tsc && tsconfig-replace-paths", "check-types": "tscw --noEmit", "dev": "yarn build:clean && yarn build:code --watch", "lint": "eslint", "postinstall": "husky install", "prepack": "pinst --disable", "postpack": "pinst --enable"}, "publishConfig": {"registry": "https://npm.pkg.github.com/", "access": "restricted"}, "files": ["dist", "CHANGELOG.md"], "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@dmeents/semantic-release-yarn": "^1.1.13", "@saithodev/semantic-release-backmerge": "^4.0.1", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@tsconfig/strictest": "^2.0.5", "@unlockre/eslint-config": "^3.0.6", "eslint": "^9.33.0", "husky": "^9.1.7", "lint-staged": "^16.1.5", "pinst": "^3.0.0", "semantic-release": "^24.2.7", "semantic-release-slack-bot": "^4.0.2", "tsconfig-replace-paths": "^0.0.14", "tscw-config": "^1.1.2", "typescript": "^5.9.2"}, "packageManager": "yarn@3.8.2"}