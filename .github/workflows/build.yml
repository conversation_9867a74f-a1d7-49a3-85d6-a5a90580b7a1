name: Build

on:
  workflow_call:

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: Clone repository
        uses: actions/checkout@v2
        with:
          persist-credentials: false

      - name: Setup Node and Yarn
        uses: unlockre/github-resources/actions/setup-node-and-yarn@main
        with:
          github-token: ${{ secrets.GH_PAT }}
        env:
          GH_PAT: ${{ secrets.GH_PAT }}

      - name: Build
        run: |
          yarn build
        env:
          GH_PAT: ${{ secrets.GH_PAT }}
