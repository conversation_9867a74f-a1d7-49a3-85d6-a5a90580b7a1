name: Development continuos integration

on:
  pull_request:
    branches:
      - "develop"
    types:
      - edited
      - opened
      - synchronize
      - reopened

jobs:
  lint-pr-title:
    name: Lint PR title
    uses: unlockre/github-resources/.github/workflows/lint-pr-title.yml@main
    secrets: inherit
    with:
      commitlint-config-path: "./commitlint.config.js"

  test-deploy:
    name: Test deploy
    uses: ./.github/workflows/deploy.yml
    secrets: inherit