name: Deploy

on:
  workflow_call:
    inputs:
      with-semantic-release:
        description: With semantic release?
        default: false
        type: boolean

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    steps:
      - name: Clone repository
        uses: actions/checkout@v2
        with:
          persist-credentials: false

      - name: Setup Node and Yarn
        uses: unlockre/github-resources/actions/setup-node-and-yarn@main
        with:
          github-token: ${{ secrets.GH_PAT }}
        env:
          GH_PAT: ${{ secrets.GH_PAT }}

      - name: Build
        run: |
          yarn build
        env:
          GH_PAT: ${{ secrets.GH_PAT }}

      - name: Semantic versioning
        if: ${{ inputs.with-semantic-release }}
        run: |
          yarn semantic-release
        env:
          GH_PAT: ${{ secrets.GH_PAT }}
          GITHUB_TOKEN: ${{ secrets.GH_PAT }}
          NPM_TOKEN: ${{ secrets.GH_PAT }}
          RELEASES_SLACK_WEBHOOK: ${{ secrets.NOTIFY_RELEASES_SLACK_WEBHOOK }}