# Package Template

## Installation

```
yarn add @unlockre/package-template
```

### Peer dependencies

- **some-dependency**: `^1.0.0"`

## Usage

## Develop

### Install yarn

We are using yarn berry.

To install yarn follow this [guide](https://yarnpkg.com/getting-started/install).

### Use Keyway JS packages

Given that we are publishing our JS packages to the [Github NPM registry](https://docs.github.com/en/packages/working-with-a-github-packages-registry/working-with-the-npm-registry), we need to configure `yarn` so it can download them from the registry, so to do this we have to:

- Get a Github Personal Access Token (**PAT**) from [here](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token)
- Define in your OS an environment variable called GH_PAT with the **PAT** you obtained in the previous step (We are leveraging a Yarn feature that allows the usage of env variables inside its config)

### VSCode configuration

There is a [.vscode](.vscode) folder that contains a couple of things:

1. A [settings.sample.json](.vscode/settings.sample.json) that you need to copy to **.vscode/settings.json** to configure `ESLint` and disable formatting on save (this is done by `ESLint`)
2. An [extensions.json](.vscode/extensions.json) file that has a list of extensions that will help you work on this project that `VSCode` will suggest you to install

### Available NPM Scripts

> You can get the following list but without the descriptions by running `yarn run`.

- `build`: Builds the project
- `dev`: Starts a process that builds the project on every change
