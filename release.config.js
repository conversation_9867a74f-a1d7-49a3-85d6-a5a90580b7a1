/**
 * @type {import('semantic-release').GlobalConfig}
 */
const releaseConfig = {
  branches: ["main"],
  plugins: [
    [
      "@semantic-release/commit-analyzer",
      {
        preset: "conventionalcommits"
      }
    ],
    "@semantic-release/release-notes-generator",
    "@semantic-release/changelog",
    "@dmeents/semantic-release-yarn",
    [
      "@semantic-release/git",
      {
        assets: ["package.json", "CHANGELOG.md"],
        message: "chore: Create release ${nextRelease.version} [skip ci]"
      }
    ],
    [
      "@saithodev/semantic-release-backmerge",
      {
        branches: [{from: "main", to: "develop"}],
        forcePush: true
      }
    ],
    "@semantic-release/github",
    [
      "semantic-release-slack-bot",
      {
        markdownReleaseNotes: true,
        notifyOnFail: true,
        notifyOnSuccess: true,
        slackWebhookEnVar: "RELEASES_SLACK_WEBHOOK"
      }
    ]
  ]
};

export default releaseConfig;
